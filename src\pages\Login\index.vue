<template>
  <div class="login-page">
    <!-- 头部导航 -->
    <div class="header">
      <div class="nav-left" @click="goBack">
        <span class="back-icon">←</span>
        <span class="close-icon">×</span>
      </div>
      <div class="nav-title">登录</div>
    </div>

    <!-- 企业文化输出区 -->
    <div class="brand-section">
      <div class="brand-image">
        <!-- 这里应该放置企业logo/文化标识/形象标志/吉祥物等元素 -->
        <div class="brand-placeholder">
          <div class="logo-circle"></div>
          <div class="brand-name">杭韵e家</div>
        </div>
      </div>
    </div>

    <!-- 登录表单 -->
    <div class="login-form">
      <!-- 手机号输入框 -->
      <div class="input-group">
        <div class="input-wrapper">
          <span class="country-code">+86</span>
          <input
            v-model="phoneNumber"
            type="tel"
            class="phone-input"
            placeholder="请输入手机号码"
            maxlength="13"
            @input="formatPhoneNumber"
            @focus="onPhoneFocus"
          />
          <i
            v-if="phoneNumber"
            class="clear-btn"
            @click="clearPhone"
          >×</i>
        </div>
      </div>

      <!-- 验证码输入框 -->
      <div class="input-group">
        <div class="input-wrapper">
          <input
            v-model="verifyCode"
            type="number"
            class="code-input"
            placeholder="请输入短信验证码"
            maxlength="6"
            @focus="onCodeFocus"
          />
          <i
            v-if="verifyCode"
            class="clear-btn"
            @click="clearCode"
          >×</i>
        </div>
        <button
          class="get-code-btn"
          :class="{ active: canGetCode, disabled: !canGetCode }"
          :disabled="!canGetCode"
          @click="getVerifyCode"
        >
          {{ codeButtonText }}
        </button>
      </div>

      <!-- 隐私协议 -->
      <div class="privacy-section">
        <label class="checkbox-wrapper">
          <input
            v-model="agreePrivacy"
            type="checkbox"
            class="checkbox"
          />
          <span class="checkmark"></span>
          <span class="privacy-text">
            我已阅读并同意
            <span class="privacy-link" @click="showPrivacyPolicy">《隐私协议》</span>
          </span>
        </label>
      </div>

      <!-- 登录按钮 -->
      <button
        class="login-btn"
        :class="{ active: canLogin, disabled: !canLogin }"
        :disabled="!canLogin"
        @click="handleLogin"
      >
        登录
      </button>

      <!-- 提示语 -->
      <div class="tip-text">
        未注册过的用户将自动创建账号
      </div>
    </div>

    <!-- 数字键盘 -->
    <div v-if="showKeyboard" class="keyboard-overlay" @click="hideKeyboard">
      <div class="keyboard" @click.stop>
        <div class="keyboard-row">
          <button class="key" @click="inputNumber('1')">1</button>
          <button class="key" @click="inputNumber('2')">2<span class="sub">ABC</span></button>
          <button class="key" @click="inputNumber('3')">3<span class="sub">DEF</span></button>
        </div>
        <div class="keyboard-row">
          <button class="key" @click="inputNumber('4')">4<span class="sub">GHI</span></button>
          <button class="key" @click="inputNumber('5')">5<span class="sub">JKL</span></button>
          <button class="key" @click="inputNumber('6')">6<span class="sub">MNO</span></button>
        </div>
        <div class="keyboard-row">
          <button class="key" @click="inputNumber('7')">7<span class="sub">PQRS</span></button>
          <button class="key" @click="inputNumber('8')">8<span class="sub">TUV</span></button>
          <button class="key" @click="inputNumber('9')">9<span class="sub">WXYZ</span></button>
        </div>
        <div class="keyboard-row">
          <button class="key empty"></button>
          <button class="key" @click="inputNumber('0')">0</button>
          <button class="key delete" @click="deleteNumber">⌫</button>
        </div>
      </div>
    </div>

    <!-- 隐私协议弹窗 -->
    <div v-if="showPrivacyModal" class="modal-overlay" @click="hidePrivacyModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>隐私协议</h3>
          <button class="close-btn" @click="hidePrivacyModal">×</button>
        </div>
        <div class="modal-body">
          <div v-html="privacyContent"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { sendSmsCode, loginBySms, getPrivacyPolicy } from '@/api/auth';
import common from '@/util/util';
import auth from '@/utils/auth';
import store from '@/store';
import request from '@/utils/request'; // 导入request实例以获取完整的response对象

export default {
  name: 'LoginPage',
  data() {
    return {
      phoneNumber: '',
      verifyCode: '',
      agreePrivacy: false,
      showKeyboard: false,
      showPrivacyModal: false,
      privacyContent: '',
      countdown: 0,
      countdownTimer: null,
      activeInput: 'phone' // 'phone' or 'code'
    };
  },
  computed: {
    // 清理后的手机号（去除格式化字符）
    cleanPhoneNumber() {
      return this.phoneNumber.replace(/\s/g, '');
    },
    // 是否可以获取验证码
    canGetCode() {
      return this.cleanPhoneNumber.length === 11 && this.countdown === 0;
    },
    // 是否可以登录
    canLogin() {
      return this.cleanPhoneNumber.length === 11 && 
             this.verifyCode.length === 6 && 
             this.agreePrivacy;
    },
    // 获取验证码按钮文案
    codeButtonText() {
      if (this.countdown > 0) {
        return `${this.countdown}s后重新发送`;
      }
      return this.cleanPhoneNumber.length === 11 && this.countdown === 0 ? '重新发送' : '获取验证码';
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 格式化手机号显示（3-3-4格式）
    formatPhoneNumber() {
      let value = this.phoneNumber.replace(/\D/g, '');
      if (value.length > 11) {
        value = value.slice(0, 11);
      }
      
      if (value.length > 3 && value.length <= 7) {
        value = value.replace(/(\d{3})(\d+)/, '$1 $2');
      } else if (value.length > 7) {
        value = value.replace(/(\d{3})(\d{4})(\d+)/, '$1 $2 $3');
      }
      
      this.phoneNumber = value;
    },
    
    // 清空手机号
    clearPhone() {
      this.phoneNumber = '';
    },
    
    // 清空验证码
    clearCode() {
      this.verifyCode = '';
    },

    // 手机号输入框获得焦点
    onPhoneFocus() {
      this.activeInput = 'phone';
      this.showKeyboard = true;
    },

    // 验证码输入框获得焦点
    onCodeFocus() {
      this.activeInput = 'code';
      this.showKeyboard = true;
    },
    
    // 隐藏键盘
    hideKeyboard() {
      this.showKeyboard = false;
    },
    
    // 输入数字
    inputNumber(num) {
      if (this.activeInput === 'phone') {
        if (this.cleanPhoneNumber.length < 11) {
          this.phoneNumber += num;
          this.formatPhoneNumber();
        }
      } else if (this.activeInput === 'code') {
        if (this.verifyCode.length < 6) {
          this.verifyCode += num;
        }
      }
    },
    
    // 删除数字
    deleteNumber() {
      if (this.activeInput === 'phone') {
        this.phoneNumber = this.phoneNumber.slice(0, -1);
      } else if (this.activeInput === 'code') {
        this.verifyCode = this.verifyCode.slice(0, -1);
      }
    },
    
    // 手机号正则校验
    validatePhone(phone) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(phone);
    },
    
    // 获取验证码
    async getVerifyCode() {
      if (!this.canGetCode) return;
      
      const phone = this.cleanPhoneNumber;
      
      // 手机号校验
      if (!this.validatePhone(phone)) {
        common.showToast('请输入正确的手机号码');
        return;
      }

      try {
        const result = await sendSmsCode(phone);
        console.log(result,"resresulturesultlt")
        // 兼容不同的响应格式
        if (result && (result.respCode === '000000' || result.success === 1 || result.code === 200)) {
          common.showToast('验证码已发送');
          this.startCountdown();
        } else {
          const errorMsg = result?.respMsg || result?.msg || '发送失败，请重试';
          common.showToast(errorMsg);
        }
      } catch (error) {
        console.error('发送验证码失败:', error);
        common.showToast('发送失败，请重试');
      }
    },
    
    // 开始倒计时
    startCountdown() {
      this.countdown = 60;
      this.countdownTimer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
        }
      }, 1000);
    },
    
    // 登录处理
      async handleLogin() {
      if (!this.canLogin) return;
      
      // 检查是否勾选协议
      if (!this.agreePrivacy) {
        common.showToast('请阅读并勾选协议');
        return;
      }

      const phone = this.cleanPhoneNumber;
      const code = this.verifyCode;

      try {
        // 直接使用request实例并设置responseType为'all'以便获取完整的response对象
        const result = await request({
          url: '/activity-manager-api/appLogin',
          method: 'post',
          data: { mobile: phone, verification: code },
          responseType: 'all'
        });
        
        // 检查响应是否成功
        if (result && result.data && result.data.respCode === '000000') {
          // 从响应头中获取token
          const token = result.headers['x-auth-token'] || result.headers['X-Auth-Token'];
          
          if (token) {
            // 保存token到本地存储和store
            auth.setToken(token);
            store.commit('SET_TOKEN', token);
          }

          // 保存用户信息
          const userInfo = result.data.data;
          localStorage.setItem('userId', userInfo.userId);
          localStorage.setItem('userInfo', JSON.stringify(userInfo));
          store.commit('SET_USER_INFO', userInfo);

          common.showToast('登录成功');

          // 跳转到来源页面或首页
          const redirect = this.$route.query.redirect || '/';
          this.$router.replace(redirect);
        } else {
          // 兼容不同的响应格式
          const errorMsg = result?.data?.respMsg || result?.respMsg || '登录失败，请重试';
          common.showToast(errorMsg);
        }

      } catch (error) {
        console.error('登录失败:', error);
        common.showToast('登录失败，请重试');
      }
    },
    
    // 显示隐私协议
    async showPrivacyPolicy() {
      try {
        const result = await getPrivacyPolicy();
        // 兼容不同的响应格式
        if (result && (result.respCode === '000000' || result.success === 1)) {
          this.privacyContent = result.data?.content || '';
          this.showPrivacyModal = true;
        } else {
          const errorMsg = result?.respMsg || '获取协议内容失败';
          common.showToast(errorMsg);
        }
      } catch (error) {
        console.error('获取隐私协议失败:', error);
        common.showToast('获取协议内容失败');
      }
    },
    
    // 隐藏隐私协议弹窗
    hidePrivacyModal() {
      this.showPrivacyModal = false;
    }
  },
  
  beforeDestroy() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  }
};
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #eee;

  .nav-left {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;

    .back-icon,
    .close-icon {
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: #333;
    }

    .back-icon {
      font-weight: bold;
    }

    .close-icon {
      font-size: 20px;
    }
  }

  .nav-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }
}

.brand-section {
  padding: 40px 0;
  text-align: center;
  background: #fff;

  .brand-image {
    .brand-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .logo-circle {
        width: 120px;
        height: 120px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        &::after {
          content: '';
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
        }
      }

      .brand-name {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        letter-spacing: 2px;
      }
    }
  }
}

.login-form {
  padding: 32px 24px;
  background: #fff;
  margin-top: 12px;
}

.input-group {
  margin-bottom: 24px;

  .input-wrapper {
    display: flex;
    align-items: center;
    height: 48px;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 0 16px;
    background: #fff;

    &:focus-within {
      border-color: #007aff;
    }

    .country-code {
      color: #333;
      font-size: 16px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .phone-input,
    .code-input {
      flex: 1;
      border: none;
      outline: none;
      font-size: 16px;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }

    .clear-btn {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #ccc;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      flex-shrink: 0;
    }
  }

  .get-code-btn {
    margin-left: 12px;
    padding: 0 16px;
    height: 48px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    white-space: nowrap;
    flex-shrink: 0;

    &.disabled {
      background: #f5f5f5;
      color: #999;
      cursor: not-allowed;
    }

    &.active {
      background: #007aff;
      color: #fff;
    }
  }
}

.privacy-section {
  margin-bottom: 32px;

  .checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;

    .checkbox {
      display: none;
    }

    .checkmark {
      width: 16px;
      height: 16px;
      border: 1px solid #ddd;
      border-radius: 3px;
      margin-right: 8px;
      position: relative;
      flex-shrink: 0;

      &::after {
        content: '';
        position: absolute;
        left: 4px;
        top: 1px;
        width: 6px;
        height: 10px;
        border: solid #fff;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        opacity: 0;
      }
    }

    .checkbox:checked + .checkmark {
      background: #007aff;
      border-color: #007aff;

      &::after {
        opacity: 1;
      }
    }

    .privacy-text {
      font-size: 14px;
      color: #666;

      .privacy-link {
        color: #007aff;
        text-decoration: underline;
      }
    }
  }
}

.login-btn {
  width: 100%;
  height: 48px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;

  &.disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }

  &.active {
    background: #007aff;
    color: #fff;
  }
}

.tip-text {
  text-align: center;
  font-size: 12px;
  color: #999;
  margin-top: 16px;
}

// 键盘样式
.keyboard-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
}

.keyboard {
  width: 100%;
  background: #d1d5db;
  padding: 8px;

  .keyboard-row {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .key {
    flex: 1;
    height: 48px;
    background: #fff;
    border: none;
    border-radius: 6px;
    font-size: 24px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;

    &:active {
      background: #e5e5e5;
    }

    &.empty {
      background: transparent;
      cursor: default;

      &:active {
        background: transparent;
      }
    }

    &.delete {
      font-size: 20px;
    }

    .sub {
      font-size: 10px;
      color: #666;
      position: absolute;
      bottom: 4px;
    }
  }
}

// 弹窗样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-content {
  background: #fff;
  border-radius: 12px;
  max-width: 90%;
  max-height: 80%;
  overflow: hidden;

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }

    .close-btn {
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      font-size: 20px;
      cursor: pointer;
      color: #666;
    }
  }

  .modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
  }
}
</style>
