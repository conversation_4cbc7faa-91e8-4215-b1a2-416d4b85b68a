<template>
  <div class="activity-detail">
    <!-- 活动内容 -->
    <div class="detail-content" v-if="activity.id">
      <!-- 活动标题滚动区域 -->
      <div class="activity-title-section">
        <div class="scrolling-title" :class="{ 'scrolling': shouldScroll }">
          <span class="title-text">{{ activity.title }}</span>
        </div>
        <div class="activity-code">活动编号：{{ activity.activityCode }}</div>
      </div>

      <!-- 活动头图 -->
      <div class="activity-header">
        <img :src="activity.imageUrl" :alt="activity.title" class="header-image" />
      </div>

      <!-- 活动状态 -->
      <div class="activity-status-section">
        <div class="status-item">
          <span class="status-label">活动状态：</span>
          <span class="status-badge" :class="getStatusClass(activity.activityStatus)">
            {{ getActivityStatusText(activity.activityStatus) }}
          </span>
        </div>
      </div>

      <!-- 活动时间信息 -->
      <div class="activity-time-section">
        <div class="time-item">
          <span class="time-label">报名时间：</span>
          <span class="time-value">{{ activity.registrationTime }}</span>
        </div>
        <div class="time-item">
          <span class="time-label">活动时间：</span>
          <span class="time-value">{{ activity.activityTime }}</span>
        </div>
      </div>

      <!-- 报名人数 -->
      <div class="participant-section">
        <span class="participant-text">报名人数：{{ activity.participantCount }}/{{ activity.maxParticipants }} 人</span>
      </div>

      <!-- 活动详情 -->
      <div class="activity-description">
        <h3>活动详情</h3>
        <div class="description-content" v-html="activity.description"></div>
      </div>

      <!-- 活动须知 -->
      <div class="activity-notice" v-if="activity.notice">
        <h3>活动须知</h3>
        <div class="notice-content" v-html="activity.notice"></div>
      </div>

      <!-- 报名信息 -->
      <div class="registration-info" v-if="activity.registrationInfo">
        <h3>报名信息</h3>
        <div class="registration-content">
          <div class="info-item" v-for="(item, index) in activity.registrationInfo" :key="index">
            <span class="label">{{ item.label }}：</span>
            <span class="value">{{ item.value }}</span>
          </div>
        </div>
      </div>

      <!-- 联系方式 -->
      <div class="contact-info" v-if="activity.contactInfo">
        <h3>联系方式</h3>
        <div class="contact-content">
          <div class="contact-item" v-for="(item, index) in activity.contactInfo" :key="index">
            <span class="label">{{ item.label }}：</span>
            <span class="value" @click="handleContact(item)">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-else class="loading-container" type="spinner" size="24px">
      加载中...
    </van-loading>

    <!-- 底部操作栏 -->
    <div class="bottom-actions" v-if="activity.id && shouldShowButton">
      <van-button
        class="register-button"
        :class="getButtonClass()"
        size="large"
        :disabled="isButtonDisabled"
        @click="handleRegister"
      >
        {{ getButtonText() }}
      </van-button>
    </div>

    <!-- 报名弹窗 -->
    <van-popup v-model="showRegisterPopup" position="bottom" :style="{ height: '60%' }">
      <div class="register-popup">
        <div class="popup-header">
          <h3>活动报名</h3>
          <van-icon name="cross" @click="showRegisterPopup = false" />
        </div>
        
        <div class="register-form">
          <van-field
            v-model="registerForm.name"
            label="姓名"
            placeholder="请输入您的姓名"
            required
          />
          <van-field
            v-model="registerForm.phone"
            label="手机号"
            placeholder="请输入您的手机号"
            type="tel"
            required
          />
          <van-field
            v-model="registerForm.cardType"
            label="证件类型"
            placeholder="请选择证件类型"
          />
          <van-field
            v-model="registerForm.idCard"
            label="身份证号"
            placeholder="请输入身份证号"
          />
          <van-field
            v-model="registerForm.gender"
            label="性别"
            placeholder="请选择性别"
          />
          <van-field
            v-model="registerForm.age"
            label="年龄"
            placeholder="请输入年龄"
            type="number"
          />
          <van-field
            v-model="registerForm.human"
            label="携带成人数"
            placeholder="请输入携带成人数"
            type="number"
          />
          <van-field
            v-model="registerForm.child"
            label="携带儿童数"
            placeholder="请输入携带儿童数"
            type="number"
          />
          <van-field
            v-model="registerForm.high"
            label="身高(cm)"
            placeholder="请输入身高"
            type="number"
          />
          <van-field
            v-model="registerForm.weight"
            label="体重(kg)"
            placeholder="请输入体重"
            type="number"
          />
          <van-field
            v-model="registerForm.educate"
            label="学历"
            placeholder="请输入学历"
          />
          <van-field
            v-model="registerForm.community"
            label="社区"
            placeholder="请输入所在社区"
          />
          <van-field
            v-model="registerForm.address"
            label="地址"
            placeholder="请输入详细地址"
            type="textarea"
            rows="2"
          />
        </div>

        <div class="popup-footer">
          <van-button type="default" @click="showRegisterPopup = false">取消</van-button>
          <van-button type="primary" @click="submitRegister" :loading="submitting">确认报名</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import Vue from 'vue';
import {
  NavBar,
  Icon,
  Button,
  Loading,
  Popup,
  Field,
  Toast,
  Dialog
} from 'vant';

Vue.use(NavBar)
  .use(Icon)
  .use(Button)
  .use(Loading)
  .use(Popup)
  .use(Field)
  .use(Toast)
  .use(Dialog);

export default {
  name: 'ActivityDetail',
  data() {
    return {
      activity: {},
      showRegisterPopup: false,
      submitting: false,
      registerForm: {
        name: '',
        phone: '',
        cardType: '',
        idCard: '',
        gender: '',
        age: '',
        human: '',
        child: '',
        high: '',
        weight: '',
        educate: '',
        community: '',
        address: '',
        selfAdds: [] // 自增项目
      }
    };
  },
  computed: {
    shouldScroll() {
      return this.activity.title && this.activity.title.length > 15;
    },

    shouldShowButton() {
      // 报名未开始或已结束时隐藏按钮
      return this.activity.registrationStatus !== 'not_started' &&
             this.activity.registrationStatus !== 'ended';
    },

    isButtonDisabled() {
      // 已报名时按钮可点击但置灰
      return false;
    }
  },
  mounted() {
    this.loadActivityDetail();
  },
  methods: {
    async loadActivityDetail() {
      try {
        const activityId = this.$route.params.id;

        // 1.5 活动详情接口调用
        const response = await this.$api.getActivityDetail(activityId);

        if (response && response.success === 1) {
          const data = response.value;

          // 根据接口文档映射数据
          this.activity = {
            id: activityId,
            title: data.actTitle, // 活动名称
            activityCode: `ACT${activityId}`,
            imageUrl: data.headerImg || require('@/images/img/background.png'), // 小封面
            headerImgId: data.headerImgId, // 小封面Id
            activityStatus: data.activityStatus, // 活动状态
            activityTime: data.actTime, // 活动时间
            registrationTime: data.actTime, // 使用活动时间作为报名时间
            location: data.locationString, // 活动地址+详细地址
            notice: data.actNotice, // 活动须知
            description: data.actDesc, // 活动介绍
            participantCount: 0, // 接口未返回，设为默认值
            maxParticipants: 'unlimited', // 接口未返回，设为不限
            userRegistered: false, // 接口未返回，设为默认值
            registrationStatus: 'ongoing' // 默认为进行中
          };
        } else {
          // 如果接口调用失败，使用模拟数据
          const mockData = {
            '000009': {
              title: '活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称',
              activityCode: '000009',
              activityStatus: 'not_started', // 未开始
              registrationStatus: 'not_started',
              participantCount: 0,
              maxParticipants: 10,
              userRegistered: false
            },
            '000007': {
              title: '活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称',
              activityCode: '000007',
              activityStatus: 'ongoing', // 进行中
              registrationStatus: 'ongoing',
              participantCount: 0,
              maxParticipants: 'unlimited', // 不限
              userRegistered: false
            },
            '000006': {
              title: '活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称活动名称',
              activityCode: '000006',
              activityStatus: 'ended', // 已结束
              registrationStatus: 'ended',
              participantCount: 0,
              maxParticipants: 10,
              userRegistered: true
            }
          };

          const data = mockData[activityId] || mockData['000009'];

          this.activity = {
            id: activityId,
            ...data,
            imageUrl: require('@/images/img/background.png'), // 使用本地图片
            activityTime: 'yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm',
            registrationTime: 'yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm'
          };
        }
      } catch (error) {
        console.error('加载活动详情失败:', error);
        Toast('加载失败，请重试');
      }
    },

    getStatusClass(status) {
      const statusMap = {
        'not_started': 'not-started',
        'ongoing': 'ongoing',
        'ended': 'ended'
      };
      return statusMap[status] || 'not-started';
    },

    getActivityStatusText(status) {
      const statusMap = {
        'not_started': '未开始',
        'ongoing': '进行中',
        'ended': '已结束'
      };
      return statusMap[status] || '未开始';
    },

    getButtonClass() {
      if (this.activity.userRegistered) {
        return 'registered';
      }
      return 'primary';
    },

    getButtonText() {
      if (this.activity.userRegistered) {
        return '已报名';
      }
      return '一键报名';
    },
    
    handleRegister() {
      if (this.activity.userRegistered) {
        // 已报名，跳转到报名详情页
        this.$router.push(`/registration-detail/${this.activity.id}`);
        return;
      }

      // 检查是否登录
      const isLoggedIn = this.checkLoginStatus();
      if (!isLoggedIn) {
        this.showLoginDialog();
        return;
      }

      // 进入报名信息填写页
      this.$router.push(`/registration-form/${this.activity.id}`);
    },

    checkLoginStatus() {
      // TODO: 检查用户登录状态
      return false; // 模拟未登录状态
    },

    showLoginDialog() {
      this.$dialog.confirm({
        title: '提示',
        message: '您尚未登录，请先登录',
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(() => {
        // 调用微信能力获取用户手机号
        this.getWechatPhone();
      }).catch(() => {
        // 用户取消
      });
    },

    getWechatPhone() {
      // TODO: 调用微信能力获取手机号
      // 获取成功后进入微信授权登录页，未获取到进入短信登录页
      console.log('获取微信手机号');
    },
    
    async submitRegister() {
      // 表单验证
      if (!this.registerForm.name.trim()) {
        Toast('请输入姓名');
        return;
      }

      if (!this.registerForm.phone.trim()) {
        Toast('请输入手机号');
        return;
      }

      if (!/^1[3-9]\d{9}$/.test(this.registerForm.phone)) {
        Toast('请输入正确的手机号');
        return;
      }

      this.submitting = true;

      try {
        // 1.9 一键报名接口调用
        const params = {
          id: this.activity.id, // 活动Id
          name: this.registerForm.name, // 姓名
          phone: this.registerForm.phone, // 手机号
          cardType: this.registerForm.cardType, // 证件类型
          idCard: this.registerForm.idCard, // 身份证号
          gender: this.registerForm.gender, // 性别
          age: this.registerForm.age, // 年龄
          human: this.registerForm.human ? parseInt(this.registerForm.human) : null, // 携带成人数
          child: this.registerForm.child ? parseInt(this.registerForm.child) : null, // 携带儿童数
          high: this.registerForm.high ? parseFloat(this.registerForm.high) : null, // 身高
          weight: this.registerForm.weight ? parseFloat(this.registerForm.weight) : null, // 体重
          educate: this.registerForm.educate, // 学历
          community: this.registerForm.community, // 社区
          address: this.registerForm.address, // 地址
          selfAdds: this.registerForm.selfAdds // 自增项目
        };

        const response = await this.$api.registerActivity(params);

        if (response && response.success === 1) {
          Toast.success('报名成功！');
          this.showRegisterPopup = false;
          this.activity.userRegistered = true;
          this.activity.participantCount++;

          // 重置表单
          this.registerForm = {
            name: '',
            phone: '',
            cardType: '',
            idCard: '',
            gender: '',
            age: '',
            human: '',
            child: '',
            high: '',
            weight: '',
            educate: '',
            community: '',
            address: '',
            selfAdds: []
          };
        } else {
          Toast(response?.respDesc || '报名失败，请重试');
        }
      } catch (error) {
        console.error('报名失败:', error);
        Toast('报名失败，请重试');
      } finally {
        this.submitting = false;
      }
    },
    
    handleContact(item) {
      if (item.type === 'phone') {
        window.location.href = `tel:${item.value}`;
      } else if (item.type === 'email') {
        window.location.href = `mailto:${item.value}`;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.activity-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px; // 为底部按钮留出空间
}

.detail-content {
  background: white;
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.activity-title-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .scrolling-title {
    height: 24px;
    overflow: hidden;
    margin-bottom: 8px;

    .title-text {
      font-size: 18px;
      font-weight: bold;
      color: #333;
      white-space: nowrap;
      display: inline-block;
    }

    &.scrolling .title-text {
      animation: scroll-text 10s linear infinite;
    }
  }

  .activity-code {
    font-size: 14px;
    color: #999;
  }
}

@keyframes scroll-text {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.activity-header {
  .header-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }
}

.activity-status-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .status-item {
    display: flex;
    align-items: center;

    .status-label {
      font-size: 14px;
      color: #666;
      margin-right: 8px;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;

      &.not-started {
        background: #f0f0f0;
        color: #666;
      }

      &.ongoing {
        background: #e8f5e8;
        color: #52c41a;
      }

      &.ended {
        background: #fff2e8;
        color: #fa8c16;
      }
    }
  }
}

.activity-time-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;

  .time-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;

    .time-label {
      color: #666;
      min-width: 80px;
    }

    .time-value {
      color: #333;
      flex: 1;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.participant-section {
  padding: 16px;

  .participant-text {
    font-size: 14px;
    color: #666;
  }
}

.activity-description,
.activity-notice,
.registration-info,
.contact-info {
  padding: 20px 16px;
  border-bottom: 8px solid #f8f8f8;
  
  h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
  }
  
  .description-content,
  .notice-content {
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    
    :deep(p) {
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

.registration-info,
.contact-info {
  .registration-content,
  .contact-content {
    .info-item,
    .contact-item {
      display: flex;
      margin-bottom: 8px;
      font-size: 14px;
      
      .label {
        color: #666;
        min-width: 80px;
      }
      
      .value {
        color: #333;
        flex: 1;
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    
    .contact-item .value {
      color: #1989fa;
      cursor: pointer;
    }
  }
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  z-index: 100;

  .register-button {
    border-radius: 24px;

    &.primary {
      background: linear-gradient(135deg, #d4af37, #b8941f);
      border: none;
      color: white;
    }

    &.registered {
      background: #f0f0f0;
      color: #999;
      border: 1px solid #ddd;
    }
  }
}

.register-popup {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      font-size: 18px;
      font-weight: bold;
    }
    
    .van-icon {
      font-size: 20px;
      color: #999;
    }
  }
  
  .register-form {
    flex: 1;
  }
  
  .popup-footer {
    display: flex;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    
    .van-button {
      flex: 1;
    }
  }
}
</style>
