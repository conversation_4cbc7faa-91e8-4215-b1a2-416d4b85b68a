<template>
  <div class="profile-container">
    <!-- 头部用户信息 -->
    <div class="profile-header">
      <div class="user-info">
        <img :src="userInfo.avatar || defaultAvatar" :alt="userInfo.name" class="user-avatar" />
        <div class="user-details">
          <h3 class="user-name">{{ userInfo.name || '未登录' }}</h3>
          <p class="user-phone">{{ userInfo.phone || '请登录' }}</p>
        </div>
        <van-icon name="arrow" class="arrow-icon" />
      </div>
    </div>

    <!-- 我的活动统计 -->
    <div class="activity-stats">
      <div class="stats-item" @click="goToMyActivities('registered')">
        <div class="stats-number">{{ activityStats.registered }}</div>
        <div class="stats-label">已报名</div>
      </div>
      <div class="stats-item" @click="goToMyActivities('participated')">
        <div class="stats-number">{{ activityStats.participated }}</div>
        <div class="stats-label">已参加</div>
      </div>
      <div class="stats-item" @click="goToMyActivities('favorite')">
        <div class="stats-number">{{ activityStats.favorite }}</div>
        <div class="stats-label">已收藏</div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell 
          title="我的报名" 
          icon="calendar-o" 
          is-link 
          @click="goToMyActivities('registered')"
        />
        <van-cell 
          title="我的收藏" 
          icon="star-o" 
          is-link 
          @click="goToMyActivities('favorite')"
        />
        <van-cell 
          title="活动历史" 
          icon="clock-o" 
          is-link 
          @click="goToMyActivities('history')"
        />
      </van-cell-group>
    </div>

    <div class="menu-section">
      <van-cell-group>
        <van-cell 
          title="个人设置" 
          icon="setting-o" 
          is-link 
          @click="goToSettings"
        />
        <van-cell 
          title="意见反馈" 
          icon="chat-o" 
          is-link 
          @click="goToFeedback"
        />
        <van-cell 
          title="关于我们" 
          icon="info-o" 
          is-link 
          @click="goToAbout"
        />
      </van-cell-group>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section" v-if="userInfo.id">
      <van-button type="default" size="large" @click="handleLogout">
        退出登录
      </van-button>
    </div>

    <!-- 登录按钮 -->
    <div class="login-section" v-else>
      <van-button type="primary" size="large" @click="handleLogin">
        立即登录
      </van-button>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import { 
  CellGroup, 
  Cell, 
  Icon, 
  Button,
  Dialog,
  Toast 
} from 'vant';

Vue.use(CellGroup)
  .use(Cell)
  .use(Icon)
  .use(Button)
  .use(Dialog)
  .use(Toast);

export default {
  name: 'Profile',
  data() {
    return {
      userInfo: {},
      activityStats: {
        registered: 0,
        participated: 0,
        favorite: 0
      },
      defaultAvatar: 'https://via.placeholder.com/60x60'
    };
  },
  mounted() {
    this.loadUserInfo();
    this.loadActivityStats();
  },
  methods: {
    async loadUserInfo() {
      try {
        // TODO: 从store或API获取用户信息
        // const userInfo = this.$store.state.userInfo;
        
        // 模拟用户数据
        this.userInfo = {
          id: '123',
          name: '张三',
          phone: '138****5678',
          avatar: 'https://via.placeholder.com/60x60'
        };
      } catch (error) {
        console.error('加载用户信息失败:', error);
      }
    },
    
    async loadActivityStats() {
      try {
        // TODO: 调用API获取活动统计数据
        // const stats = await this.$api.getActivityStats();
        
        // 模拟统计数据
        this.activityStats = {
          registered: 5,
          participated: 3,
          favorite: 8
        };
      } catch (error) {
        console.error('加载活动统计失败:', error);
      }
    },
    
    goToMyActivities(type) {
      if (!this.userInfo.id) {
        this.handleLogin();
        return;
      }
      
      // TODO: 跳转到我的活动页面
      this.$router.push({
        name: 'myActivities',
        query: { type }
      });
    },
    
    goToSettings() {
      if (!this.userInfo.id) {
        this.handleLogin();
        return;
      }
      
      // TODO: 跳转到设置页面
      this.$router.push({ name: 'settings' });
    },
    
    goToFeedback() {
      // TODO: 跳转到意见反馈页面
      this.$router.push({ name: 'feedback' });
    },
    
    goToAbout() {
      // TODO: 跳转到关于我们页面
      this.$router.push({ name: 'about' });
    },
    
    handleLogin() {
      // TODO: 实现登录逻辑
      Toast('请实现登录功能');
    },
    
    handleLogout() {
      Dialog.confirm({
        title: '确认退出',
        message: '确定要退出登录吗？'
      }).then(() => {
        // TODO: 实现退出登录逻辑
        this.userInfo = {};
        this.activityStats = {
          registered: 0,
          participated: 0,
          favorite: 0
        };
        Toast.success('已退出登录');
      }).catch(() => {
        // 用户取消
      });
    }
  }
};
</script>

<style lang="less" scoped>
.profile-container {
  min-height: 100vh;
  background: #f8f8f8;
}

.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 16px;
  color: white;
  
  .user-info {
    display: flex;
    align-items: center;
    
    .user-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      margin-right: 16px;
    }
    
    .user-details {
      flex: 1;
      
      .user-name {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 4px;
      }
      
      .user-phone {
        font-size: 14px;
        opacity: 0.8;
      }
    }
    
    .arrow-icon {
      color: rgba(255, 255, 255, 0.6);
    }
  }
}

.activity-stats {
  display: flex;
  background: white;
  margin: -20px 16px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  
  .stats-item {
    flex: 1;
    text-align: center;
    padding: 20px 0;
    cursor: pointer;
    
    &:not(:last-child) {
      border-right: 1px solid #f0f0f0;
    }
    
    .stats-number {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 4px;
    }
    
    .stats-label {
      font-size: 12px;
      color: #666;
    }
    
    &:active {
      background: #f8f8f8;
    }
  }
}

.menu-section {
  margin-bottom: 16px;
  
  .van-cell-group {
    margin: 0 16px;
    border-radius: 8px;
    overflow: hidden;
  }
}

.logout-section,
.login-section {
  padding: 20px 16px;
  
  .van-button {
    border-radius: 24px;
  }
}

.logout-section .van-button {
  background: white;
  color: #666;
  border: 1px solid #ddd;
}

.login-section .van-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}
</style>
