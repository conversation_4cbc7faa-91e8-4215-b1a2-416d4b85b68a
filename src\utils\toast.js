/*
 * @Author: Activity H5 Project
 * @Date: 2025-01-01
 * @Description: Toast轻提示工具
 */

let toastInstance = null;

// 创建Toast元素
function createToast(message, duration = 2000) {
  // 如果已存在toast，先移除
  if (toastInstance) {
    document.body.removeChild(toastInstance);
    toastInstance = null;
  }

  // 创建toast元素
  const toast = document.createElement('div');
  toast.className = 'custom-toast';
  toast.textContent = message;
  
  // 添加样式
  Object.assign(toast.style, {
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    background: 'rgba(0, 0, 0, 0.8)',
    color: '#fff',
    padding: '12px 20px',
    borderRadius: '8px',
    fontSize: '14px',
    zIndex: '9999',
    maxWidth: '80%',
    textAlign: 'center',
    wordBreak: 'break-all',
    opacity: '0',
    transition: 'opacity 0.3s ease'
  });

  // 添加到页面
  document.body.appendChild(toast);
  toastInstance = toast;

  // 显示动画
  setTimeout(() => {
    toast.style.opacity = '1';
  }, 10);

  // 自动隐藏
  setTimeout(() => {
    if (toast && toast.parentNode) {
      toast.style.opacity = '0';
      setTimeout(() => {
        if (toast && toast.parentNode) {
          document.body.removeChild(toast);
          if (toastInstance === toast) {
            toastInstance = null;
          }
        }
      }, 300);
    }
  }, duration);
}

// 显示Toast
export function showToast(message, duration = 2000) {
  createToast(message, duration);
}

// 显示成功提示
export function showSuccess(message, duration = 2000) {
  showToast(message, duration);
}

// 显示错误提示
export function showError(message, duration = 2000) {
  showToast(message, duration);
}

// 显示警告提示
export function showWarning(message, duration = 2000) {
  showToast(message, duration);
}

// 隐藏Toast
export function hideToast() {
  if (toastInstance && toastInstance.parentNode) {
    toastInstance.style.opacity = '0';
    setTimeout(() => {
      if (toastInstance && toastInstance.parentNode) {
        document.body.removeChild(toastInstance);
        toastInstance = null;
      }
    }, 300);
  }
}

export default {
  show: showToast,
  success: showSuccess,
  error: showError,
  warning: showWarning,
  hide: hideToast
};
